# 1. 用户的输入问题
# 2. 问题发散并决定调用工具
# 3. 并发调用 ToolCall
    # 3.1 EmbeddingSearch (input: new query, repo basic info, output: code snippets )
        # 3.1.1 问题重写 -> 查询的代码/文档的描述性文字
        # 3.1.2 是否被Emedding过？没有则对当前仓库进行Embedding
        # 3.1.3 调用远程Embedding服务进行搜索
        # 3.1.4 代码片段去重
    # 3.2 Term-Parse Search (input: query, output: code snippets )
        # 3.2.1 问题重写 -> 重写为可能出现在文档中的描述或者代码
        # 3.2.2 是否有本地缓存信息？有则加载，无则对当前仓库进行索引
        # 3.2.3 代码片段去重
    # 3.3 GrepSearch ( input: grep keywords, output: code snippets )
        # 3.3.1 问题重写 -> 重写为可被grep搜索到的关键词
        # 3.3.2 执行grep搜索
        # 3.3.3 代码片段去重
    # 3.4 ReadFile( input: file path, output: file content )
        # 3.4.1 读取文件内容
    # 3.5 ReadDir( input: dir path, output: file list )
        # 3.5.1 读取目录内容
    # 3.6 GraphSerch( input: node, search type, search depth, output: code snippets )
        # 3.6.1 问题重写 -> 重写为可被图搜索理解的查询
        # 3.6.2 是否有本地图信息？有则加载，无则对当前仓库进行图索引
        # 3.6.3 代码片段去重
# 4. 汇总结果
# 5. 判断是否符合需要

from core.config import Config

class SearchRouter:
    def __init__(self, config: Config):
        pass
