"""
支持 trace_id 的日志模块
扩展原有的日志系统，添加 trace_id 支持
"""

import logging
from typing import Optional, Dict, Any
from utils.trace_context import get_trace_id, get_trace_context, format_trace_info


class TraceFormatter(logging.Formatter):
    """
    支持 trace_id 的日志格式器
    """
    
    def __init__(self, fmt: Optional[str] = None, datefmt: Optional[str] = None, 
                 include_trace: bool = True, trace_format: str = "[{trace_info}]"):
        """
        初始化格式器
        
        Args:
            fmt: 日志格式字符串
            datefmt: 日期格式字符串
            include_trace: 是否包含 trace 信息
            trace_format: trace 信息的格式模板
        """
        # 如果没有提供格式，使用默认格式
        if fmt is None:
            if include_trace:
                fmt = '%(asctime)s - %(name)s - %(levelname)s - %(trace_info)s - %(message)s'
            else:
                fmt = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        super().__init__(fmt, datefmt)
        self.include_trace = include_trace
        self.trace_format = trace_format
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
        
        Returns:
            str: 格式化后的日志字符串
        """
        # 添加 trace 信息到记录中
        if self.include_trace:
            trace_info = self._get_trace_info()
            record.trace_info = trace_info if trace_info else "no_trace"
        
        return super().format(record)
    
    def _get_trace_info(self) -> str:
        """
        获取格式化的 trace 信息
        
        Returns:
            str: 格式化的 trace 信息
        """
        trace_id = get_trace_id()
        if not trace_id:
            return ""
        
        # 构建 trace 信息
        trace_parts = [f"trace_id={trace_id}"]
        
        # 添加额外的上下文信息
        context = get_trace_context()
        for key, value in context.items():
            if key != 'trace_id':  # 避免重复
                trace_parts.append(f"{key}={value}")
        
        trace_info = " | ".join(trace_parts)
        return self.trace_format.format(trace_info=trace_info)


class TraceAwareLogger:
    """
    支持 trace_id 的日志记录器包装类
    """
    
    def __init__(self, logger: logging.Logger):
        """
        初始化包装器
        
        Args:
            logger: 要包装的 logger 对象
        """
        self.logger = logger
    
    def _log_with_trace(self, level: int, msg: str, *args, **kwargs):
        """
        带 trace 信息的日志记录
        
        Args:
            level: 日志级别
            msg: 日志消息
            *args: 消息参数
            **kwargs: 额外参数
        """
        # 获取 trace 信息
        trace_id = get_trace_id()
        context = get_trace_context()
        
        # 如果有 trace_id，添加到 extra 中
        extra = kwargs.get('extra', {})
        if trace_id:
            extra['trace_id'] = trace_id
            extra.update(context)
        
        kwargs['extra'] = extra
        self.logger.log(level, msg, *args, **kwargs)
    
    def debug(self, msg: str, *args, **kwargs):
        """记录 DEBUG 级别日志"""
        self._log_with_trace(logging.DEBUG, msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """记录 INFO 级别日志"""
        self._log_with_trace(logging.INFO, msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """记录 WARNING 级别日志"""
        self._log_with_trace(logging.WARNING, msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """记录 ERROR 级别日志"""
        self._log_with_trace(logging.ERROR, msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """记录 CRITICAL 级别日志"""
        self._log_with_trace(logging.CRITICAL, msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        """记录异常日志"""
        kwargs['exc_info'] = True
        self._log_with_trace(logging.ERROR, msg, *args, **kwargs)
    
    # 代理其他方法到原始 logger
    def __getattr__(self, name):
        return getattr(self.logger, name)


def get_trace_logger(name: Optional[str] = None) -> TraceAwareLogger:
    """
    获取支持 trace_id 的日志记录器
    
    Args:
        name: logger 名称，如果为 None 则自动获取调用模块名称
    
    Returns:
        TraceAwareLogger: 支持 trace_id 的日志记录器
    """
    if name is None:
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    # 获取原始 logger
    from utils.logger import _get_original_logger
    original_logger = _get_original_logger(name)
    
    # 包装为 trace-aware logger
    return TraceAwareLogger(original_logger)


def setup_trace_logging():
    """
    设置支持 trace_id 的日志系统
    更新现有的日志处理器以使用 TraceFormatter
    """
    from utils.logger import logger, file_handler, console_handler
    
    # 创建支持 trace 的格式器
    trace_formatter = TraceFormatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(trace_info)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 更新文件处理器的格式器
    file_handler.setFormatter(trace_formatter)
    
    # 为控制台创建简化的格式器（Rich 处理器）
    console_trace_formatter = TraceFormatter(
        fmt='%(trace_info)s - %(message)s',
        include_trace=True
    )
    
    # 注意：RichHandler 可能需要特殊处理
    # 这里我们保持原有的控制台格式，只更新文件格式
    
    return trace_formatter


def log_function_entry_exit(func_name: Optional[str] = None):
    """
    装饰器：记录函数的进入和退出
    
    Args:
        func_name: 自定义函数名称，如果为 None 则使用实际函数名
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            name = func_name or func.__name__
            logger = get_trace_logger(func.__module__)
            
            logger.info(f"进入函数: {name}")
            try:
                result = func(*args, **kwargs)
                logger.info(f"退出函数: {name}")
                return result
            except Exception as e:
                logger.error(f"函数执行异常: {name}, 错误: {str(e)}")
                raise
        
        return wrapper
    return decorator


def log_execution_time(func_name: Optional[str] = None):
    """
    装饰器：记录函数执行时间
    
    Args:
        func_name: 自定义函数名称
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        import functools
        import time
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            name = func_name or func.__name__
            logger = get_trace_logger(func.__module__)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                logger.info(f"函数 {name} 执行完成，耗时: {execution_time:.3f}s")
                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(f"函数 {name} 执行失败，耗时: {execution_time:.3f}s, 错误: {str(e)}")
                raise
        
        return wrapper
    return decorator


# 便捷的全局 trace logger 实例
trace_logger = get_trace_logger(__name__)


# 便捷函数
def trace_info(msg: str, *args, **kwargs):
    """记录带 trace 的 INFO 日志"""
    trace_logger.info(msg, *args, **kwargs)


def trace_warning(msg: str, *args, **kwargs):
    """记录带 trace 的 WARNING 日志"""
    trace_logger.warning(msg, *args, **kwargs)


def trace_error(msg: str, *args, **kwargs):
    """记录带 trace 的 ERROR 日志"""
    trace_logger.error(msg, *args, **kwargs)


def trace_debug(msg: str, *args, **kwargs):
    """记录带 trace 的 DEBUG 日志"""
    trace_logger.debug(msg, *args, **kwargs)


def trace_critical(msg: str, *args, **kwargs):
    """记录带 trace 的 CRITICAL 日志"""
    trace_logger.critical(msg, *args, **kwargs)
