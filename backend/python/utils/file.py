from enum import Enum
from hashlib import sha256
from functools import lru_cache
from pydantic import BaseModel, Field
from pathlib import Path
from typing import List, Optional, Union, Dict
import fnmatch

from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

class FileType(Enum):
    CODE = "code"
    DOC = "doc"
    
    @classmethod
    def from_suffix(cls, suffix: str):
        if suffix in [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs", ".php", ".rb"]:
            return cls.CODE
        else:
            return cls.DOC
        
class FileNode(BaseModel):
    id: str = Field(default_factory=str)
    name: str = Field(default_factory=str)
    type: str = Field(default_factory=str)  # 'file' or 'directory'
    path: str = Field(default_factory=str)
    size: Optional[int] = Field(default=0)
    lastModified: Optional[str] = Field(default="")
    children: Optional[List['FileNode']] = Field(default_factory=list)

    def __str__(self):
        """返回文件树的树形字符串表示"""
        return self._to_tree_string()

    def _to_tree_string(self, prefix: str = "", is_last: bool = True) -> str:
        """
        递归生成树形字符串表示

        Args:
            prefix: 当前行的前缀字符串
            is_last: 是否是同级中的最后一个节点

        Returns:
            树形字符串
        """
        # 当前节点的符号
        current_symbol = "└── " if is_last else "├── "

        # 当前节点的完整行
        result = prefix + current_symbol + self.name + "\n"

        # 如果有子节点，递归处理
        if self.children:
            # 为子节点准备前缀
            child_prefix = prefix + ("    " if is_last else "│   ")

            for i, child in enumerate(self.children):
                is_child_last = (i == len(self.children) - 1)
                result += child._to_tree_string(child_prefix, is_child_last)

        return result
    
# 工具函数
def get_file_size(file_path: Path) -> int:
    """获取文件大小"""
    try:
        return file_path.stat().st_size
    except:
        return 0

def get_last_modified(file_path: Path) -> str:
    """获取文件最后修改时间"""
    try:
        return file_path.stat().st_mtime.__str__()
    except:
        return ""

def get_file_filter_config():
    """获取文件过滤配置"""
    try:
        config = get_config()
        return {
            'exclude': config.file_filter.exclude,
            'include': config.file_filter.include,
            'max_file_size': config.file_filter.max_file_size
        }
    except Exception:
        # 配置加载失败时使用默认配置
        return {
            'exclude': ['.git', '.svn', '.hg', 'node_modules', '__pycache__', '.pytest_cache',
                       'target', 'build', 'dist', '.next', '.vscode', '.idea', '.DS_Store',
                       '*.pyc', '*.pyo', '*.pyd', '.venv'],
            'include': [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp",
                       ".go", ".rs", ".php", ".rb", ".md"],
            'max_file_size': 1048576  # 1MB
        }


def should_ignore_path(path: Union[Path | str]) -> bool:
    """根据配置文件中的设置判断是否应该忽略某个路径"""
    filter_config = get_file_filter_config()
    exclude_patterns = filter_config['exclude']
    include_extensions = filter_config['include']
    max_file_size = filter_config['max_file_size']

    if isinstance(path, str):
        path = Path(path)
    name = path.name

    # 检查排除模式
    for pattern in exclude_patterns:
        if '*' in pattern:
            # 处理通配符模式，如 *.pyc
            if fnmatch.fnmatch(name, pattern):
                return True
        else:
            # 处理精确匹配或目录名
            if name == pattern:
                logger.info(f"Exclude path: {path} by pattern: {pattern}")
                return True

    # 如果是文件，检查扩展名和大小
    if path.is_file():
        # 检查文件扩展名是否在包含列表中
        file_extension = path.suffix.lower()
        if include_extensions and file_extension not in include_extensions:
            return True

        # 检查文件大小
        try:
            if path.stat().st_size > max_file_size:
                return True
        except (OSError, PermissionError):
            # 如果无法获取文件大小，默认不忽略
            pass

    return False

@lru_cache(maxsize=128)
def build_file_tree(dir_path: Path, max_depth: int = 10, current_depth: int = 0) -> List[FileNode]:
    """构建文件树"""
    if current_depth >= max_depth:
        return []

    nodes = []
    try:
        # 自定义排序：目录在前，文件在后，然后按名称字母顺序排序
        def sort_key(item):
            # 目录返回 (0, 名称)，文件返回 (1, 名称)
            return (0 if item.is_dir() else 1, item.name.lower())

        for item in sorted(dir_path.iterdir(), key=sort_key):
            if should_ignore_path(item):
                continue

            node = FileNode(
                id=str(item),
                name=item.name,
                type="directory" if item.is_dir() else "file",
                path=str(item),
            )

            if item.is_file():
                node.size = get_file_size(item)
                node.lastModified = get_last_modified(item)
            elif item.is_dir():
                # 递归获取子目录
                node.children = build_file_tree(
                    item, max_depth, current_depth + 1
                )

            nodes.append(node)
    except PermissionError:
        pass

    return nodes

def build_file_list(dir_path: Union[str | Path]) -> Dict[str, str]:
    """
    递归读取project_dir中的文件，忽略ignores中的目录或文件
    在读取文件时，在每行的文件前加上行号信息，如 l:{line num} | {code}
    返回文件路径到内容的映射
    """
    doc_content = {}
    if isinstance(dir_path, str):
        dir_path = Path(dir_path)
    
    file_nodes = build_file_tree(dir_path, max_depth=100)

    def traverse_and_read(node: FileNode):
        if node.type == "file":
            try:
                with open(node.path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    doc_content[node.path] = "\n".join(lines)
            except Exception as e:
                logger.error(f"Error reading file {node.path}: {e}")
        else:
            for child in node.children:
                traverse_and_read(child)
    
    for node in file_nodes:
        traverse_and_read(node)
    
    return doc_content


def generate_file_hash_name(file_path: Union[str | Path], hash_len: str = 32):
    if isinstance(file_path, Path):
        file_path = str(file_path)
    
    assert len(file_path) > 0 and hash_len < 256

    return sha256(file_path.encode()).hexdigest()[:hash_len]