import json
from typing import List
from pathlib import Path
from fastapi import FastAP<PERSON>, APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware

from server.api.v1.schema import WorkspaceInfo, SearchRequest, SearchResponse
from modules.deepsearch.deep_search import DeepSearch, SearchToolEnum
from utils.file import FileNode
from core.config import get_config
from utils.file import should_ignore_path, build_file_tree, get_file_size

# 创建API路由器
router = APIRouter(prefix="/api/v1")

# 创建FastAPI应用
app = FastAPI(title="Codebase API", version="1.0.0")

# 配置跨域中间件
config = get_config()
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.api.cors.origins,
    allow_credentials=config.api.cors.allow_credentials,
    allow_methods=config.api.cors.allow_methods,
    allow_headers=config.api.cors.allow_headers,
)

@router.get("/workspaces", response_model=List[WorkspaceInfo])
async def get_workspaces():
    """获取所有工作区"""
    workspaces = []

    repos_path = Path(get_config().data.repos_path)
    if not repos_path.exists():
        raise HTTPException(status_code=404, detail="Repos directory not found")

    try:
        for item in repos_path.iterdir():
            if item.is_dir() and not should_ignore_path(item):
                description = f"{item.name} repository"

                workspace = WorkspaceInfo(
                    id=item.name,
                    name=item.name,
                    path=str(repos_path / item.name),
                    description=description
                )
                workspaces.append(workspace)

        return workspaces
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read workspaces: {str(e)}")


@router.get("/workspaces/{workspace_name}/files", response_model=List[FileNode])
async def get_workspace_files(workspace_name: str):
    """获取工作区文件树"""
    workspace_path = Path(get_config().data.repos_path) / workspace_name

    if not workspace_path.exists():
        raise HTTPException(status_code=404, detail="Workspace not found")

    if not workspace_path.is_dir():
        raise HTTPException(status_code=400, detail="Workspace is not a directory")

    try:
        file_tree = build_file_tree(workspace_path)
        return file_tree
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file tree: {str(e)}")


@router.get("/files")
async def get_file_content(file_path: str = Query(..., description="File path relative to repos")):
    """获取文件内容"""
    # 安全检查：确保路径在 repos 目录内
    try:
        # 获取repos根目录
        repos_path = Path(get_config().data.repos_path)

        # 构建完整路径（相对于repos目录）
        full_path = repos_path / file_path

        # 解析路径并确保在repos目录内
        full_path_resolved = full_path.resolve()
        repos_path_resolved = repos_path.resolve()

        if not str(full_path_resolved).startswith(str(repos_path_resolved)):
            raise HTTPException(status_code=403, detail="Access denied")

        if not full_path_resolved.exists():
            raise HTTPException(status_code=404, detail="File not found")

        if not full_path_resolved.is_file():
            raise HTTPException(status_code=400, detail="Path is not a file")

        # 读取文件内容
        try:
            with open(full_path_resolved, 'r', encoding='utf-8') as f:
                content = f.read()
            return {"content": content}
        except UnicodeDecodeError:
            # 如果是二进制文件，返回提示信息
            return {"content": f"Binary file: {full_path_resolved.name}\nFile size: {get_file_size(full_path_resolved)} bytes"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read file: {str(e)}")


@router.post("/search")
async def search_code(request: SearchRequest) -> SearchResponse:
    """处理代码搜索请求 - 流式返回搜索过程"""

    # 设置 trace_id 上下文
    from utils.trace_context import TraceContextManager
    from utils.trace_logger import get_trace_logger

    # 使用请求中的 trace_id，并添加额外的上下文信息
    with TraceContextManager(
        trace_id=request.trace_id,
        workspace=request.workspace_name,
        search_tool=request.search_tool,
        is_stream=request.is_stream
    ):
        # 获取支持 trace 的 logger
        logger = get_trace_logger(__name__)
        logger.info(f"开始处理搜索请求: query='{request.query}', workspace='{request.workspace_name}'")

        workspace_path = Path(get_config().data.repos_path) / request.workspace_name
        if not workspace_path.exists():
            logger.error(f"工作空间不存在: {workspace_path}")
            raise HTTPException(status_code=404, detail="Workspace not found")

        deep_search = DeepSearch(str(workspace_path.absolute()), search_tool=SearchToolEnum(request.search_tool))

        if not request.is_stream:
            logger.info("执行异步非流式搜索")
            result = await deep_search.search_async(request.query)
            logger.info(f"异步搜索完成，找到 {len(result.code_snippets)} 个代码片段")
            return SearchResponse(query = result.original_query, code_snippets=json.dumps([snippet.model_dump() for snippet in result.code_snippets], ensure_ascii=False))

        logger.info("执行流式搜索")
        return StreamingResponse(
            deep_search.search_stream(request.query),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "X-Accel-Buffering": "no",  # 禁用nginx缓冲
            "Transfer-Encoding": "chunked"  # 确保分块传输
        }
    )

# 将路由器包含到应用中（必须在所有路由定义之后）
app.include_router(router)
