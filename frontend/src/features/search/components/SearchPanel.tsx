import React, { useState, useEffect, useRef } from 'react';
import { Button, Input } from '@/components/ui';
import { Search, X, Clock, ChevronDown, ChevronUp } from 'lucide-react';
import { useWorkspace } from '@/features/workspace/hooks';
import { useAppStore } from '@/store/appStore';
import { SearchResultList } from './SearchResultList';
import type { SearchRequest, SearchResultData, CodeSnippet } from '@/types';

interface ProgressMessage {
  message: string;
  timestamp: number;
}

interface SearchPanelProps {
  onSearch: (request: SearchRequest) => void;
  loading?: boolean;
  onClear?: () => void;
  searchResult?: string;
  searchResultData?: SearchResultData | null;
  searchError?: string | null;
  searchTime?: number;
  progressMessages?: ProgressMessage[];
  onSnippetClick?: (snippet: CodeSnippet) => void;
}

export function SearchPanel({
  onSearch,
  loading = false,
  onClear,
  searchResult: _searchResult, // 重命名为 _searchResult 表示未使用
  searchResultData,
  searchError,
  searchTime,
  progressMessages = [],
  onSnippetClick
}: SearchPanelProps) {
  const [query, setQuery] = useState('');
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isProcessCollapsed, setIsProcessCollapsed] = useState(false);
  const { getCurrentWorkspace } = useWorkspace();
  const { searchType } = useAppStore();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const currentWorkspace = getCurrentWorkspace;

  // 管理计时器
  useEffect(() => {
    if (loading) {
      // 开始搜索时启动计时器
      startTimeRef.current = Date.now();
      setElapsedTime(0);

      timerRef.current = setInterval(() => {
        if (startTimeRef.current) {
          const elapsed = (Date.now() - startTimeRef.current) / 1000;
          setElapsedTime(elapsed);
        }
      }, 100); // 每100ms更新一次
    } else {
      // 搜索结束时清除计时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      startTimeRef.current = null;
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [loading]);

  // 自动滚动到最新消息
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [progressMessages]);

  // 搜索完成且有结果时自动折叠搜索过程
  useEffect(() => {
    if (!loading && searchResultData && searchResultData.code_snippets.length > 0) {
      setIsProcessCollapsed(true);
    }
  }, [loading, searchResultData]);

  const handleSearch = () => {
    if (!query.trim() || !currentWorkspace) return;

    // 开始新搜索时重置折叠状态
    setIsProcessCollapsed(false);

    const request: SearchRequest = {
      traceId: Math.random().toString(36).substring(2),
      query: query.trim(),
      workspaceId: currentWorkspace.id,
      searchType, // 使用全局设置中的searchType
      options: {
        maxResults: 50,
      },
    };

    onSearch(request);
  };

  const handleClear = () => {
    setQuery('');
    onClear?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSearch();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setQuery(e.target.value);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-semibold text-gray-900">搜索查询</h3>
        {onClear && (
          <Button
            variant="ghost"
            size="sm"
            icon={<X className="h-3 w-3" />}
            onClick={handleClear}
          >
            清空
          </Button>
        )}
      </div>

      {/* 搜索表单区域 - 固定高度 */}
      <div className="flex-shrink-0 space-y-3">
        <Input
          type="textarea"
          placeholder="输入要搜索的内容...&#10;支持多行输入，按 Enter 搜索"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={loading}
          className="min-h-[80px]"
        />

        <div className="flex items-center gap-3">
          <Button
            variant="primary"
            size="md"
            icon={<Search className="h-4 w-4" />}
            onClick={handleSearch}
            loading={loading}
            disabled={!query.trim() || !currentWorkspace || loading}
            className="flex-1"
          >
            {loading ? '搜索中...' : '开始搜索'}
          </Button>

          {loading && (
            <div className="flex items-center gap-1 text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
              <Clock className="h-3 w-3" />
              <span>{elapsedTime.toFixed(1)}s</span>
            </div>
          )}
        </div>
      </div>

      {/* 工作区警告 - 固定高度 */}
      {!currentWorkspace && (
        <div className="flex-shrink-0 text-sm text-warning-500 bg-warning-50 p-3 rounded-md">
          请先选择一个工作区
        </div>
      )}

      {/* 搜索过程显示区域 */}
      {progressMessages.length > 0 && (
        <div className={`flex flex-col border-t border-gray-200 pt-4 mt-4 ${isProcessCollapsed ? 'flex-shrink-0' : 'flex-1 min-h-0'}`}>
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <button
              onClick={() => setIsProcessCollapsed(!isProcessCollapsed)}
              className="flex items-center gap-2 text-sm font-semibold text-gray-900 hover:text-blue-600 transition-colors"
            >
              <span>搜索过程</span>
              {isProcessCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </button>
            <div className="flex items-center gap-1 text-sm">
              {loading ? (
                <>
                  <Clock className="h-3 w-3 animate-spin text-blue-600" />
                  <span className="text-blue-600">进行中... {elapsedTime.toFixed(1)}s</span>
                </>
              ) : (
                <>
                  <span className="text-green-600">✅ 已完成</span>
                  {searchTime !== undefined && searchTime > 0 && (
                    <span className="text-gray-500 ml-2">
                      耗时: {searchTime.toFixed(2)}ms
                    </span>
                  )}
                </>
              )}
            </div>
          </div>

          {!isProcessCollapsed && (
            <div className="flex-1 bg-blue-50 rounded-md p-4 overflow-y-auto min-h-0">
              <div className="space-y-2">
                {progressMessages.map((messageObj, index) => (
                  <div key={index} className="text-base text-gray-800 font-mono leading-relaxed">
                    <span className="text-blue-600 text-sm font-bold">
                      [{new Date(messageObj.timestamp).toLocaleTimeString()}]
                    </span>{' '}
                    <span className="whitespace-pre-wrap">{messageObj.message}</span>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>
          )}
        </div>
      )}

      {/* 搜索错误显示 */}
      {!loading && searchError && progressMessages.length === 0 && (
        <div className="flex-1 flex flex-col border-t border-gray-200 pt-4 mt-4 min-h-0">
          <div className="text-sm text-error-500 bg-error-50 p-3 rounded-md">
            ❌ 搜索失败: {searchError}
          </div>
        </div>
      )}

      {/* 搜索结果展示区域 */}
      {!loading && searchResultData && searchResultData.code_snippets.length > 0 && (
        <div className={`flex flex-col pt-4 mt-4 min-h-0 ${
          progressMessages.length > 0 && !isProcessCollapsed
            ? 'flex-1 border-t border-gray-200'
            : 'flex-1'
        }`}>
          <div className="flex items-center justify-between mb-3 flex-shrink-0">
            <h3 className="text-sm font-semibold text-gray-900">搜索结果</h3>
            <div className="text-xs text-gray-500">
              {searchResultData.total_snippets} 个片段，{searchResultData.total_files} 个文件
            </div>
          </div>

          <div className="flex-1 overflow-y-auto min-h-0">
            <SearchResultList
              searchResult={searchResultData}
              onSnippetClick={onSnippetClick}
            />
          </div>
        </div>
      )}
    </div>
  );
}
