import { useState, useEffect } from 'react';
import { fileSystemService } from '@/services/fileSystem';
import { getFileExtension, getLanguageFromExtension } from '@/utils/format';

export function useFileContent(filePath?: string) {
  const [content, setContent] = useState<string>('');
  const [language, setLanguage] = useState<string>('text');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载文件内容
  const loadFileContent = async (path: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const fileContent = await fileSystemService.getFileContent(path);
      setContent(fileContent);
      
      // 根据文件扩展名确定语言
      const extension = getFileExtension(path);
      const detectedLanguage = getLanguageFromExtension(extension);
      setLanguage(detectedLanguage);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载文件内容失败');
      setContent('');
    } finally {
      setLoading(false);
    }
  };

  // 清空内容
  const clearContent = () => {
    setContent('');
    setLanguage('text');
    setError(null);
  };

  // 当文件路径变化时重新加载内容
  useEffect(() => {
    if (filePath) {
      loadFileContent(filePath);
    } else {
      clearContent();
    }
  }, [filePath]);

  return {
    content,
    language,
    loading,
    error,
    reload: () => filePath && loadFileContent(filePath),
    clear: clearContent,
  };
}
